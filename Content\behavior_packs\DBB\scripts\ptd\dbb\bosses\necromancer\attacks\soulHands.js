import { system } from "@minecraft/server";
import { spawnEntitiesWithInterval } from "../../../utilities/summonEntity";
import { getRandomLocation } from "../../../utilities/vector3";
import { getTarget } from "../../general_mechanics/targetUtils";
/**
 * Attack timing constants for soul hands attack phases
 */
const SUMMON_TIMING = 45; // Summon skeleton souls at tick 45
/**
 * Total animation time in ticks
 */
const ANIMATION_TIME = 90;
/**
 * Cooldown time in ticks after the attack completes
 */
const COOLDOWN_TIME = 20;
/**
 * Configuration for the soul hands attack
 */
const SOUL_HANDS_CONFIG = {
    /** Minimum number of minions to summon */
    MIN_MINION_COUNT: 3,
    /** Maximum number of minions to summon */
    MAX_MINION_COUNT: 5
};
/**
 * Executes the soul hands attack for the Necromancer using the new timing system
 * Uses localized runTimeout for summoning, reset, and cooldown
 *
 * @param necromancer The necromancer entity
 */
export function executeSoulHandsAttack(necromancer) {
    // Summon skeleton souls at tick 45
    let summonTiming = system.runTimeout(() => {
        try {
            const isDead = necromancer.getProperty("ptd_dbb:dead");
            if (isDead) {
                system.clearRun(summonTiming);
                return;
            }
            if (necromancer.getProperty("ptd_dbb:attack") === "soul_hands") {
                performSoulHandsSummon(necromancer);
            }
        }
        catch (error) {
            system.clearRun(summonTiming);
        }
    }, SUMMON_TIMING);
    // Reset attack after animation completes
    let resetTiming = system.runTimeout(() => {
        try {
            const isDead = necromancer.getProperty("ptd_dbb:dead");
            if (isDead) {
                system.clearRun(resetTiming);
                return;
            }
            if (necromancer.getProperty("ptd_dbb:attack") === "soul_hands") {
                necromancer.triggerEvent("ptd_dbb:reset_attack");
            }
        }
        catch (error) {
            system.clearRun(resetTiming);
        }
    }, ANIMATION_TIME);
    // Set cooldown after reset
    let cooldownTiming = system.runTimeout(() => {
        try {
            const isDead = necromancer.getProperty("ptd_dbb:dead");
            if (isDead) {
                system.clearRun(cooldownTiming);
                return;
            }
            necromancer.setProperty("ptd_dbb:cooling_down", false);
        }
        catch (error) {
            system.clearRun(cooldownTiming);
        }
    }, ANIMATION_TIME + COOLDOWN_TIME);
}
/**
 * Performs the soul hands summoning logic
 * @param necromancer The necromancer entity
 */
async function performSoulHandsSummon(necromancer) {
    try {
        // Get the target
        const target = getTarget(necromancer, necromancer.location, 32, ["necromancer"]);
        // If no target, abort the attack
        if (!target) {
            return;
        }
        // Generate a random count for skeleton souls to summon
        const skeletonSoulCount = Math.floor(SOUL_HANDS_CONFIG.MIN_MINION_COUNT + Math.random() * (SOUL_HANDS_CONFIG.MAX_MINION_COUNT - SOUL_HANDS_CONFIG.MIN_MINION_COUNT + 1));
        const entityConfigs = [
            {
                entityId: "ptd_dbb:skeleton_soul",
                count: skeletonSoulCount
            }
        ];
        // Spawn the minions with a delay between each
        await spawnEntitiesWithInterval(necromancer.dimension, entityConfigs, () => {
            // Get a random position around the TARGET
            const pos = getRandomLocation(target.location, necromancer.dimension, 3, // Base offset (minimum distance from target)
            5, // Additional random offset
            0, // No Y offset
            true // Check for air block
            );
            // Add visual effects if position is valid
            if (pos) {
                necromancer.dimension.playSound("mob.ptd_dbb_necromancer.soul_hands", pos);
            }
            return pos;
        }, 1, // Delay between spawns
        (entity) => {
            // Play sound effect when entity is spawned
            necromancer.dimension.playSound("mob.skeleton.say", entity.location);
            necromancer.dimension.spawnParticle("ptd_dbb:nec_soulhands1_01", entity.location);
        });
    }
    catch (error) {
        console.warn("Error in performSoulHandsSummon:", error);
    }
}
