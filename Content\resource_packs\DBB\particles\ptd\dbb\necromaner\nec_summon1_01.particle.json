{"format_version": "1.10.0", "particle_effect": {"description": {"identifier": "ptd_dbb:nec_summon1_01", "basic_render_parameters": {"material": "particles_add", "texture": "textures/ptd/dbb/particles/bosses/necromancer/circle_02"}}, "curves": {"variable.life": {"type": "catmull_rom", "input": "v.particle_age", "horizontal_range": "v.particle_lifetime", "nodes": [0.21, 0, 8.82, -0.6]}, "variable.slow": {"type": "catmull_rom", "input": "v.particle_age", "horizontal_range": "v.particle_lifetime", "nodes": [0, 2.86, 3.52, 2.31, 0]}}, "events": {"event": {"particle_effect": {"effect": "ptd_dbb:nec_summon1_02", "type": "emitter"}}}, "components": {"minecraft:emitter_rate_instant": {"num_particles": 3}, "minecraft:emitter_lifetime_looping": {"active_time": 2}, "minecraft:emitter_lifetime_events": {"creation_event": "event"}, "minecraft:emitter_shape_point": {"offset": [0, 0.1, 0]}, "minecraft:particle_lifetime_expression": {"max_lifetime": 0.5}, "minecraft:particle_initial_spin": {"rotation": "math.random(-90,90)*4", "rotation_rate": 360}, "minecraft:particle_initial_speed": 0, "minecraft:particle_motion_dynamic": {}, "minecraft:particle_appearance_billboard": {"size": ["(v.life)/2.5", "(v.life)/2.5"], "facing_camera_mode": "emitter_transform_xz", "uv": {"texture_width": 16, "texture_height": 16, "uv": [0, 0], "uv_size": [16, 16]}}, "minecraft:particle_appearance_tinting": {"color": {"interpolant": "v.particle_age / v.particle_lifetime", "gradient": {"0.0": "#00000000", "0.42": "#334EB1FF", "1.0": "#00000000"}}}}}}