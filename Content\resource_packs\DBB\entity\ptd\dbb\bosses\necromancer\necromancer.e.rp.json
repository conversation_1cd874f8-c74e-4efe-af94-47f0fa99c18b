{
  "format_version": "1.10.0",
  "minecraft:client_entity": {
    "description": {
      "identifier": "ptd_dbb:necromancer",
      "min_engine_version": "1.16.0",
      "materials": {
        "default": "entity_emissive_alpha",
        "book_aura": "emissive_translucent"
      },
      "textures": {
        "default": "textures/ptd/dbb/entity/bosses/necromancer/default"
      },
      "geometry": {
        "default": "geometry.ptd_dbb_necromancer"
      },
      "animations": {
        "look_at_target": "animation.common.look_at_target",
        "no_effects": "animation.ptd_dbb.necromancer.no_effects",
        "no_effects_except_portal": "animation.ptd_dbb.necromancer.no_effects_except_portal",
        "no_effects_except_orb": "animation.ptd_dbb.necromancer.no_effects_except_orb",
        "no_effects_except_shadows": "animation.ptd_dbb.necromancer.no_effects_except_shadows",
        "idle_particle_1": "animation.ptd_dbb.necromancer.idle_particle_1",
        "idle_particle_2": "animation.ptd_dbb.necromancer.idle_particle_2",
        "idle_particle_3": "animation.ptd_dbb.necromancer.idle_particle_3",
        "idle_particle_4": "animation.ptd_dbb.necromancer.idle_particle_4",
        "idle_particle_5": "animation.ptd_dbb.necromancer.idle_particle_5",
        "spawn": "animation.ptd_dbb.necromancer.spawn",
        "idle": "animation.ptd_dbb.necromancer.idle",
        "move": "animation.ptd_dbb.necromancer.move",
        "death": "animation.ptd_dbb.necromancer.death",
        "cataclysm": "animation.ptd_dbb.necromancer.cataclysm",
        "book_of_the_damned": "animation.ptd_dbb.necromancer.book_of_the_damned",
        "soul_drain": "animation.ptd_dbb.necromancer.soul_drain",
        "phantom_phase_start": "animation.ptd_dbb.necromancer.phantom_phase_start",
        "phantom_phase_end": "animation.ptd_dbb.necromancer.phantom_phase_end",
        "undead_summon": "animation.ptd_dbb.necromancer.undead_summon",
        "arcane_blast": "animation.ptd_dbb.necromancer.arcane_blast",
        "soul_hands": "animation.ptd_dbb.necromancer.soul_hands",
        "soul_trap": "animation.ptd_dbb.necromancer.soul_trap",
        "general": "controller.animation.ptd_dbb.necromancer.general",
        "look_at_target_controller": "controller.animation.ptd_dbb.necromancer.look_at_target"
      },
      "scripts": {
        "should_update_bones_and_effects_offscreen": true,
        "animate": [
          "general",
          "look_at_target_controller",
          "idle_particle_4",
          "idle_particle_5",
          {
            "idle_particle_1": "q.property('ptd_dbb:spawning') == false && q.property('ptd_dbb:dead') == false"
          },
          {
            "idle_particle_2": "q.property('ptd_dbb:spawning') == false && q.property('ptd_dbb:dead') == false"
          },
          {
            "idle_particle_3": "q.property('ptd_dbb:spawning') == false && q.property('ptd_dbb:dead') == false"
          },
          // Disable all effects including the orb when not using a "cataclysm" attack or phantom phase
          {
            "no_effects": "q.property('ptd_dbb:spawning') == false && q.property('ptd_dbb:dead') == false && q.property('ptd_dbb:attack') != 'cataclysm' && q.property('ptd_dbb:attack') != 'phantom_phase_start' && q.property('ptd_dbb:attack') != 'phantom_phase_end'"
          },
          // Disable all effects except the orb when using a "cataclysm" attack
          {
            "no_effects_except_orb": "q.property('ptd_dbb:spawning') == false && q.property('ptd_dbb:dead') == false && (q.property('ptd_dbb:attack') == 'cataclysm')"
          },
          // Disable all effects except the portal when spawning
          {
            "no_effects_except_portal": "q.property('ptd_dbb:spawning') == true"
          },
          // Disable all effects except the shadows when using a "phantom_phase_start" and "phantom_phase_end" attack
          {
            "no_effects_except_shadows": "q.property('ptd_dbb:spawning') == false && q.property('ptd_dbb:dead') == false && (q.property('ptd_dbb:attack') == 'phantom_phase_start' || q.property('ptd_dbb:attack') == 'phantom_phase_end')"
          }
        ]
      },
      "render_controllers": ["controller.render.ptd_dbb.necromancer"],
      "particle_effects": {
        "nec_spawn1_01": "ptd_dbb:nec_spawn1_01",
        "nec_die1_01": "ptd_dbb:nec_die1_01",
        "nec_idle1_01": "ptd_dbb:nec_idle1_01",
        "nec_idle1_02": "ptd_dbb:nec_idle1_02",
        "nec_idle1_03": "ptd_dbb:nec_idle1_03",
        "nec_head1_01": "ptd_dbb:nec_head1_01",
        "nec_head4_01": "ptd_dbb:nec_head4_01",
        "nec_phantom1_01": "ptd_dbb:nec_phantom1_01",
        "nec_phantom2_01": "ptd_dbb:nec_phantom2_01",
        "nec_phantom2_02": "ptd_dbb:nec_phantom2_02",
        "nec_phantom2_03": "ptd_dbb:nec_phantom2_03",
        "nec_souldrain1_01": "ptd_dbb:nec_souldrain1_01",
        "nec_souldrain1_02": "ptd_dbb:nec_souldrain1_02",
        "nec_souldrain1_03": "ptd_dbb:nec_souldrain1_03",
        "nec_souldrain1_04": "ptd_dbb:nec_souldrain1_04",
        "nec_souldrain1_05": "ptd_dbb:nec_souldrain1_05",
        "nec_souldrain1_06": "ptd_dbb:nec_souldrain1_06",
        "nec_souldrain2_01": "ptd_dbb:nec_souldrain2_01",
        "nec_arcane1_01": "ptd_dbb:nec_arcane1_01",
        "nec_arcane1_02": "ptd_dbb:nec_arcane1_02",
        "nec_arcane2_01": "ptd_dbb:nec_arcane2_01",
        "nec_arcane3_01": "ptd_dbb:nec_arcane3_01",
        "nec_cata1_01": "ptd_dbb:nec_cata1_01",
        "nec_castinghand1_02": "ptd_dbb:nec_castinghand1_02",
        "nec_book1_01": "ptd_dbb:nec_book1_01",
        "nec_book1_02": "ptd_dbb:nec_book1_02",
        "nec_book1_03": "ptd_dbb:nec_book1_03"
      },
      "sound_effects": {
        "spawn": "mob.ptd_dbb_necromancer.spawn",
        "cataclysm": "mob.ptd_dbb_necromancer.cataclysm",
        "book_of_the_damned": "mob.ptd_dbb_necromancer.cataclysm",
        "soul_drain": "mob.ptd_dbb_necromancer.soul_drain",
        "phantom_phase_start": "mob.ptd_dbb_necromancer.phantom_phase_start",
        "phantom_phase_end": "mob.ptd_dbb_necromancer.phantom_phase_end",
        "arcane_blast": "mob.ptd_dbb_necromancer.arcane_blast",
        "soul_hands": "mob.ptd_dbb_necromancer.soul_hands",
        "soul_trap": "mob.ptd_dbb_necromancer.soul_hands",
        "undead_summon": "mob.ptd_dbb_necromancer.undead_summon"
      },
      "spawn_egg": {
        "base_color": "#cfb43a",
        "overlay_color": "#26bccd"
      }
    }
  }
}
